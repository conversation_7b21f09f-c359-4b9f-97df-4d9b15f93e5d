/**
 * @file fifo.c
 * @brief FIFO (First In First Out) 队列模块实现
 * <AUTHOR> Assistant
 * @date 2025-07-26
 */

#include "fifo.h"
#include <stdlib.h>
#include <string.h>

/* ========== 内部辅助函数 ========== */

/**
 * @brief 线程锁初始化
 */
static fifo_result_t fifo_lock_init(fifo_t* fifo) {
#if FIFO_ENABLE_THREAD_SAFE
#if FIFO_THREAD_SAFE_TYPE == 0
    if (pthread_mutex_init(&fifo->mutex, NULL) != 0) {
        return FIFO_ERROR_THREAD_LOCK;
    }
#elif FIFO_THREAD_SAFE_TYPE == 1
    InitializeCriticalSection(&fifo->cs);
#else
    fifo->lock = 0;
#endif
#endif
    return FIFO_SUCCESS;
}

/**
 * @brief 线程锁销毁
 */
static void fifo_lock_destroy(fifo_t* fifo) {
#if FIFO_ENABLE_THREAD_SAFE
#if FIFO_THREAD_SAFE_TYPE == 0
    pthread_mutex_destroy(&fifo->mutex);
#elif FIFO_THREAD_SAFE_TYPE == 1
    DeleteCriticalSection(&fifo->cs);
#endif
#endif
}

/**
 * @brief 加锁
 */
static void fifo_lock(fifo_t* fifo) {
#if FIFO_ENABLE_THREAD_SAFE
#if FIFO_THREAD_SAFE_TYPE == 0
    pthread_mutex_lock(&fifo->mutex);
#elif FIFO_THREAD_SAFE_TYPE == 1
    EnterCriticalSection(&fifo->cs);
#else
    while (__sync_lock_test_and_set(&fifo->lock, 1)) {
        // 自旋等待
    }
#endif
#endif
}

/**
 * @brief 解锁
 */
static void fifo_unlock(fifo_t* fifo) {
#if FIFO_ENABLE_THREAD_SAFE
#if FIFO_THREAD_SAFE_TYPE == 0
    pthread_mutex_unlock(&fifo->mutex);
#elif FIFO_THREAD_SAFE_TYPE == 1
    LeaveCriticalSection(&fifo->cs);
#else
    __sync_lock_release(&fifo->lock);
#endif
#endif
}

/**
 * @brief 触发错误回调
 */
static void fifo_trigger_error(fifo_t* fifo, fifo_result_t error_code, const char* msg) {
#if FIFO_ENABLE_ERROR_CALLBACK
    if (fifo && fifo->error_callback) {
        fifo->error_callback(error_code, msg, fifo->error_callback_data);
    }
#endif
    FIFO_DEBUG_PRINT("FIFO Error: %s (code: %d)", msg, error_code);
}

/**
 * @brief 参数验证
 */
static fifo_result_t fifo_validate_params(const fifo_t* fifo) {
    if (!fifo) {
        return FIFO_ERROR_NULL_POINTER;
    }
    if (!fifo->is_initialized) {
        return FIFO_ERROR_NOT_INITIALIZED;
    }
    return FIFO_SUCCESS;
}

/* ========== 基本操作实现 ========== */

fifo_t* fifo_create(uint32_t size) {
    if (size < FIFO_MIN_SIZE || size > FIFO_MAX_SIZE) {
        FIFO_DEBUG_PRINT("Invalid size: %u", size);
        return NULL;
    }

    fifo_t* fifo = (fifo_t*)malloc(sizeof(fifo_t));
    if (!fifo) {
        FIFO_DEBUG_PRINT("Failed to allocate FIFO structure");
        return NULL;
    }

    memset(fifo, 0, sizeof(fifo_t));

#if FIFO_ENABLE_DYNAMIC_MEMORY
    fifo->buffer = (void**)malloc(size * sizeof(void*));
    if (!fifo->buffer) {
        free(fifo);
        FIFO_DEBUG_PRINT("Failed to allocate buffer");
        return NULL;
    }
#endif

    fifo->size = size;
    fifo->head = 0;
    fifo->tail = 0;
    fifo->count = 0;

    if (fifo_lock_init(fifo) != FIFO_SUCCESS) {
#if FIFO_ENABLE_DYNAMIC_MEMORY
        free(fifo->buffer);
#endif
        free(fifo);
        return NULL;
    }

#if FIFO_ENABLE_STATISTICS
    memset(&fifo->stats, 0, sizeof(fifo_stats_t));
#endif

    fifo->is_initialized = true;
    FIFO_DEBUG_PRINT("FIFO created with size %u", size);
    return fifo;
}

fifo_result_t fifo_init(fifo_t* fifo, void** buffer, uint32_t size) {
    if (!fifo || !buffer) {
        return FIFO_ERROR_NULL_POINTER;
    }
    
    if (size < FIFO_MIN_SIZE || size > FIFO_MAX_SIZE) {
        return FIFO_ERROR_INVALID_SIZE;
    }

    memset(fifo, 0, sizeof(fifo_t));
    fifo->buffer = buffer;
    fifo->size = size;
    fifo->head = 0;
    fifo->tail = 0;
    fifo->count = 0;

    fifo_result_t result = fifo_lock_init(fifo);
    if (result != FIFO_SUCCESS) {
        return result;
    }

#if FIFO_ENABLE_STATISTICS
    memset(&fifo->stats, 0, sizeof(fifo_stats_t));
#endif

    fifo->is_initialized = true;
    FIFO_DEBUG_PRINT("FIFO initialized with size %u", size);
    return FIFO_SUCCESS;
}

fifo_result_t fifo_destroy(fifo_t* fifo) {
    if (!fifo) {
        return FIFO_ERROR_NULL_POINTER;
    }

    if (fifo->is_initialized) {
        fifo_lock_destroy(fifo);
        
#if FIFO_ENABLE_DYNAMIC_MEMORY
        if (fifo->buffer) {
            free(fifo->buffer);
        }
#endif
        
        fifo->is_initialized = false;
        FIFO_DEBUG_PRINT("FIFO destroyed");
    }

    free(fifo);
    return FIFO_SUCCESS;
}

fifo_result_t fifo_enqueue(fifo_t* fifo, void* data) {
    fifo_result_t result = fifo_validate_params(fifo);
    if (result != FIFO_SUCCESS) {
        fifo_trigger_error(fifo, result, "Invalid parameters for enqueue");
        return result;
    }

    fifo_lock(fifo);

    if (fifo->count >= fifo->size) {
#if FIFO_ENABLE_OVERFLOW_PROTECTION
        // 覆盖最旧的数据
        fifo->head = (fifo->head + 1) % fifo->size;
        fifo->count--;
#if FIFO_ENABLE_STATISTICS
        fifo->stats.overflow_count++;
#endif
#else
        fifo_unlock(fifo);
        fifo_trigger_error(fifo, FIFO_ERROR_FULL, "FIFO is full");
        return FIFO_ERROR_FULL;
#endif
    }

    fifo->buffer[fifo->tail] = data;
    fifo->tail = (fifo->tail + 1) % fifo->size;
    fifo->count++;

#if FIFO_ENABLE_STATISTICS
    fifo->stats.enqueue_count++;
    if (fifo->count > fifo->stats.peak_usage) {
        fifo->stats.peak_usage = fifo->count;
    }
#endif

    fifo_unlock(fifo);
    FIFO_DEBUG_PRINT("Enqueued data, count: %u", fifo->count);
    return FIFO_SUCCESS;
}

fifo_result_t fifo_dequeue(fifo_t* fifo, void** data) {
    fifo_result_t result = fifo_validate_params(fifo);
    if (result != FIFO_SUCCESS) {
        fifo_trigger_error(fifo, result, "Invalid parameters for dequeue");
        return result;
    }

    if (!data) {
        fifo_trigger_error(fifo, FIFO_ERROR_NULL_POINTER, "Output data pointer is NULL");
        return FIFO_ERROR_NULL_POINTER;
    }

    fifo_lock(fifo);

    if (fifo->count == 0) {
        fifo_unlock(fifo);
        fifo_trigger_error(fifo, FIFO_ERROR_EMPTY, "FIFO is empty");
        return FIFO_ERROR_EMPTY;
    }

    *data = fifo->buffer[fifo->head];
    fifo->head = (fifo->head + 1) % fifo->size;
    fifo->count--;

#if FIFO_ENABLE_STATISTICS
    fifo->stats.dequeue_count++;
#endif

    fifo_unlock(fifo);
    FIFO_DEBUG_PRINT("Dequeued data, count: %u", fifo->count);
    return FIFO_SUCCESS;
}

fifo_result_t fifo_peek(fifo_t* fifo, void** data) {
    fifo_result_t result = fifo_validate_params(fifo);
    if (result != FIFO_SUCCESS) {
        return result;
    }

    if (!data) {
        return FIFO_ERROR_NULL_POINTER;
    }

    fifo_lock(fifo);

    if (fifo->count == 0) {
        fifo_unlock(fifo);
        return FIFO_ERROR_EMPTY;
    }

    *data = fifo->buffer[fifo->head];
    fifo_unlock(fifo);
    return FIFO_SUCCESS;
}

/* ========== 查询接口实现 ========== */

uint32_t fifo_count(const fifo_t* fifo) {
    if (fifo_validate_params(fifo) != FIFO_SUCCESS) {
        return 0;
    }
    return fifo->count;
}

uint32_t fifo_capacity(const fifo_t* fifo) {
    if (fifo_validate_params(fifo) != FIFO_SUCCESS) {
        return 0;
    }
    return fifo->size;
}

bool fifo_is_empty(const fifo_t* fifo) {
    return fifo_count(fifo) == 0;
}

bool fifo_is_full(const fifo_t* fifo) {
    if (fifo_validate_params(fifo) != FIFO_SUCCESS) {
        return false;
    }
    return fifo->count >= fifo->size;
}

uint32_t fifo_available_space(const fifo_t* fifo) {
    if (fifo_validate_params(fifo) != FIFO_SUCCESS) {
        return 0;
    }
    return fifo->size - fifo->count;
}

/* ========== 控制接口实现 ========== */

fifo_result_t fifo_clear(fifo_t* fifo) {
    fifo_result_t result = fifo_validate_params(fifo);
    if (result != FIFO_SUCCESS) {
        return result;
    }

    fifo_lock(fifo);
    fifo->head = 0;
    fifo->tail = 0;
    fifo->count = 0;
    fifo_unlock(fifo);

    FIFO_DEBUG_PRINT("FIFO cleared");
    return FIFO_SUCCESS;
}

fifo_result_t fifo_reset(fifo_t* fifo) {
    return fifo_clear(fifo);
}

/* ========== 错误处理实现 ========== */

fifo_result_t fifo_resize(fifo_t* fifo, uint32_t new_size) {
    fifo_result_t result = fifo_validate_params(fifo);
    if (result != FIFO_SUCCESS) {
        return result;
    }

    if (new_size < FIFO_MIN_SIZE || new_size > FIFO_MAX_SIZE) {
        return FIFO_ERROR_INVALID_SIZE;
    }

#if FIFO_ENABLE_DYNAMIC_MEMORY
    fifo_lock(fifo);

    void** new_buffer = (void**)malloc(new_size * sizeof(void*));
    if (!new_buffer) {
        fifo_unlock(fifo);
        return FIFO_ERROR_MEMORY_ALLOC;
    }

    // 复制现有数据到新缓冲区
    uint32_t copy_count = (fifo->count < new_size) ? fifo->count : new_size;
    for (uint32_t i = 0; i < copy_count; i++) {
        new_buffer[i] = fifo->buffer[(fifo->head + i) % fifo->size];
    }

    free(fifo->buffer);
    fifo->buffer = new_buffer;
    fifo->size = new_size;
    fifo->head = 0;
    fifo->tail = copy_count;
    fifo->count = copy_count;

    fifo_unlock(fifo);
    FIFO_DEBUG_PRINT("FIFO resized to %u", new_size);
    return FIFO_SUCCESS;
#else
    return FIFO_ERROR_INVALID_PARAM; // 静态分配不支持调整大小
#endif
}

/* ========== 批量操作实现 ========== */

#if FIFO_ENABLE_BATCH_OPERATIONS
fifo_result_t fifo_enqueue_batch(fifo_t* fifo, void** data_array,
                                  uint32_t count, uint32_t* enqueued) {
    fifo_result_t result = fifo_validate_params(fifo);
    if (result != FIFO_SUCCESS) {
        return result;
    }

    if (!data_array || count == 0) {
        return FIFO_ERROR_INVALID_PARAM;
    }

    fifo_lock(fifo);

    uint32_t available = fifo->size - fifo->count;
    uint32_t to_enqueue = (count < available) ? count : available;

    for (uint32_t i = 0; i < to_enqueue; i++) {
        fifo->buffer[fifo->tail] = data_array[i];
        fifo->tail = (fifo->tail + 1) % fifo->size;
        fifo->count++;
    }

#if FIFO_ENABLE_STATISTICS
    fifo->stats.enqueue_count += to_enqueue;
    if (fifo->count > fifo->stats.peak_usage) {
        fifo->stats.peak_usage = fifo->count;
    }
#endif

    if (enqueued) {
        *enqueued = to_enqueue;
    }

    fifo_unlock(fifo);
    FIFO_DEBUG_PRINT("Batch enqueued %u items", to_enqueue);
    return (to_enqueue == count) ? FIFO_SUCCESS : FIFO_ERROR_FULL;
}

fifo_result_t fifo_dequeue_batch(fifo_t* fifo, void** data_array,
                                  uint32_t count, uint32_t* dequeued) {
    fifo_result_t result = fifo_validate_params(fifo);
    if (result != FIFO_SUCCESS) {
        return result;
    }

    if (!data_array || count == 0) {
        return FIFO_ERROR_INVALID_PARAM;
    }

    fifo_lock(fifo);

    uint32_t to_dequeue = (count < fifo->count) ? count : fifo->count;

    for (uint32_t i = 0; i < to_dequeue; i++) {
        data_array[i] = fifo->buffer[fifo->head];
        fifo->head = (fifo->head + 1) % fifo->size;
        fifo->count--;
    }

#if FIFO_ENABLE_STATISTICS
    fifo->stats.dequeue_count += to_dequeue;
#endif

    if (dequeued) {
        *dequeued = to_dequeue;
    }

    fifo_unlock(fifo);
    FIFO_DEBUG_PRINT("Batch dequeued %u items", to_dequeue);
    return (to_dequeue == count) ? FIFO_SUCCESS : FIFO_ERROR_EMPTY;
}
#endif

/* ========== 统计信息实现 ========== */

#if FIFO_ENABLE_STATISTICS
fifo_result_t fifo_get_statistics(const fifo_t* fifo, fifo_stats_t* stats) {
    fifo_result_t result = fifo_validate_params(fifo);
    if (result != FIFO_SUCCESS) {
        return result;
    }

    if (!stats) {
        return FIFO_ERROR_NULL_POINTER;
    }

    *stats = fifo->stats;
    return FIFO_SUCCESS;
}

fifo_result_t fifo_reset_statistics(fifo_t* fifo) {
    fifo_result_t result = fifo_validate_params(fifo);
    if (result != FIFO_SUCCESS) {
        return result;
    }

    fifo_lock(fifo);
    memset(&fifo->stats, 0, sizeof(fifo_stats_t));
    fifo_unlock(fifo);

    FIFO_DEBUG_PRINT("Statistics reset");
    return FIFO_SUCCESS;
}
#endif

/* ========== 错误处理实现 ========== */

#if FIFO_ENABLE_ERROR_CALLBACK
fifo_result_t fifo_set_error_callback(fifo_t* fifo,
                                       fifo_error_callback_t callback,
                                       void* user_data) {
    fifo_result_t result = fifo_validate_params(fifo);
    if (result != FIFO_SUCCESS) {
        return result;
    }

    fifo->error_callback = callback;
    fifo->error_callback_data = user_data;
    return FIFO_SUCCESS;
}
#endif

const char* fifo_get_error_string(fifo_result_t error_code) {
    switch (error_code) {
        case FIFO_SUCCESS:
            return "Success";
        case FIFO_ERROR_NULL_POINTER:
            return "Null pointer error";
        case FIFO_ERROR_INVALID_SIZE:
            return "Invalid size parameter";
        case FIFO_ERROR_MEMORY_ALLOC:
            return "Memory allocation failed";
        case FIFO_ERROR_FULL:
            return "FIFO is full";
        case FIFO_ERROR_EMPTY:
            return "FIFO is empty";
        case FIFO_ERROR_INVALID_PARAM:
            return "Invalid parameter";
        case FIFO_ERROR_THREAD_LOCK:
            return "Thread lock operation failed";
        case FIFO_ERROR_NOT_INITIALIZED:
            return "FIFO not initialized";
        default:
            return "Unknown error";
    }
}
