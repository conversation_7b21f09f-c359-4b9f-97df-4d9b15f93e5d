# FIFO模块 Makefile
# Author: AI Assistant
# Date: 2025-07-26

# 编译器设置
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2 -g
LDFLAGS = 

# 检测操作系统
UNAME_S := $(shell uname -s)
ifeq ($(UNAME_S),Linux)
    LDFLAGS += -lpthread
endif
ifeq ($(UNAME_S),Darwin)
    LDFLAGS += -lpthread
endif

# 目录设置
SRC_DIR = .
OBJ_DIR = obj
BIN_DIR = bin

# 源文件和目标文件
SOURCES = fifo.c example.c
OBJECTS = $(SOURCES:%.c=$(OBJ_DIR)/%.o)
TARGET = $(BIN_DIR)/fifo_example

# 头文件
HEADERS = fifo.h fifo_config.h

# 默认目标
all: $(TARGET)

# 创建目录
$(OBJ_DIR):
	mkdir -p $(OBJ_DIR)

$(BIN_DIR):
	mkdir -p $(BIN_DIR)

# 编译目标文件
$(OBJ_DIR)/%.o: $(SRC_DIR)/%.c $(HEADERS) | $(OBJ_DIR)
	$(CC) $(CFLAGS) -c $< -o $@

# 链接可执行文件
$(TARGET): $(OBJECTS) | $(BIN_DIR)
	$(CC) $(OBJECTS) -o $@ $(LDFLAGS)

# 运行示例
run: $(TARGET)
	./$(TARGET)

# 调试版本
debug: CFLAGS += -DFIFO_ENABLE_DEBUG=1 -DDEBUG -g3
debug: $(TARGET)

# 线程安全版本
threadsafe: CFLAGS += -DFIFO_ENABLE_THREAD_SAFE=1
threadsafe: $(TARGET)

# 静态内存版本
static: CFLAGS += -DFIFO_ENABLE_DYNAMIC_MEMORY=0
static: $(TARGET)

# 性能测试版本
performance: CFLAGS = -Wall -O3 -DNDEBUG -DFIFO_ENABLE_DEBUG=0
performance: $(TARGET)

# 内存检查（需要valgrind）
memcheck: $(TARGET)
	valgrind --leak-check=full --show-leak-kinds=all --track-origins=yes ./$(TARGET)

# 代码覆盖率测试（需要gcov）
coverage: CFLAGS += --coverage
coverage: LDFLAGS += --coverage
coverage: $(TARGET)
	./$(TARGET)
	gcov $(SOURCES)
	lcov --capture --directory . --output-file coverage.info
	genhtml coverage.info --output-directory coverage_html

# 静态分析（需要cppcheck）
analyze:
	cppcheck --enable=all --std=c99 --platform=unix64 $(SRC_DIR)/*.c $(SRC_DIR)/*.h

# 格式化代码（需要clang-format）
format:
	clang-format -i -style="{BasedOnStyle: Google, IndentWidth: 4, ColumnLimit: 100}" $(SRC_DIR)/*.c $(SRC_DIR)/*.h

# 清理
clean:
	rm -rf $(OBJ_DIR) $(BIN_DIR)
	rm -f *.gcov *.gcda *.gcno coverage.info
	rm -rf coverage_html

# 深度清理
distclean: clean
	rm -f *~ *.bak

# 安装（复制头文件到系统目录）
install: $(HEADERS)
	sudo cp $(HEADERS) /usr/local/include/
	sudo cp $(OBJ_DIR)/fifo.o /usr/local/lib/libfifo.a

# 卸载
uninstall:
	sudo rm -f /usr/local/include/fifo.h
	sudo rm -f /usr/local/include/fifo_config.h
	sudo rm -f /usr/local/lib/libfifo.a

# 创建静态库
lib: $(OBJ_DIR)/fifo.o | $(BIN_DIR)
	ar rcs $(BIN_DIR)/libfifo.a $(OBJ_DIR)/fifo.o

# 创建共享库
shared: $(OBJ_DIR)/fifo.o | $(BIN_DIR)
	$(CC) -shared -fPIC $(OBJ_DIR)/fifo.o -o $(BIN_DIR)/libfifo.so $(LDFLAGS)

# 压缩打包
package: clean
	tar -czf fifo_module.tar.gz *.c *.h Makefile README.md

# 帮助信息
help:
	@echo "FIFO模块编译选项:"
	@echo "  all         - 编译默认版本"
	@echo "  debug       - 编译调试版本"
	@echo "  threadsafe  - 编译线程安全版本"
	@echo "  static      - 编译静态内存版本"
	@echo "  performance - 编译性能优化版本"
	@echo "  run         - 编译并运行示例"
	@echo "  lib         - 创建静态库"
	@echo "  shared      - 创建共享库"
	@echo "  memcheck    - 内存检查（需要valgrind）"
	@echo "  coverage    - 代码覆盖率测试（需要gcov/lcov）"
	@echo "  analyze     - 静态代码分析（需要cppcheck）"
	@echo "  format      - 代码格式化（需要clang-format）"
	@echo "  clean       - 清理编译文件"
	@echo "  distclean   - 深度清理"
	@echo "  install     - 安装到系统"
	@echo "  uninstall   - 从系统卸载"
	@echo "  package     - 打包源码"
	@echo "  help        - 显示此帮助信息"

# 测试不同配置
test-configs:
	@echo "测试默认配置..."
	$(MAKE) clean && $(MAKE) run
	@echo "测试调试配置..."
	$(MAKE) clean && $(MAKE) debug && ./$(TARGET)
	@echo "测试静态内存配置..."
	$(MAKE) clean && $(MAKE) static && ./$(TARGET)
	@echo "所有配置测试完成！"

# 伪目标
.PHONY: all run debug threadsafe static performance memcheck coverage analyze format clean distclean install uninstall lib shared package help test-configs

# 依赖关系
$(OBJ_DIR)/fifo.o: fifo.c fifo.h fifo_config.h
$(OBJ_DIR)/example.o: example.c fifo.h fifo_config.h
