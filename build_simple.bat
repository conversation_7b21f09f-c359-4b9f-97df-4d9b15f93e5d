@echo off
REM 简单编译脚本 - 使用cl.exe (Visual Studio编译器)
echo FIFO模块简单编译脚本

REM 尝试使用Visual Studio编译器
where cl.exe >nul 2>&1
if %errorlevel% equ 0 (
    echo 使用Visual Studio编译器...
    cl.exe /TC /W3 fifo.c simple_test.c /Fe:fifo_test.exe
    if %errorlevel% equ 0 (
        echo 编译成功！
        echo 运行测试...
        fifo_test.exe
    ) else (
        echo 编译失败
    )
    goto end
)

REM 尝试使用MinGW GCC
where gcc.exe >nul 2>&1
if %errorlevel% equ 0 (
    echo 使用MinGW GCC编译器...
    gcc -std=c99 -Wall -O2 fifo.c simple_test.c -o fifo_test.exe
    if %errorlevel% equ 0 (
        echo 编译成功！
        echo 运行测试...
        fifo_test.exe
    ) else (
        echo 编译失败
    )
    goto end
)

REM 尝试使用TCC (Tiny C Compiler)
where tcc.exe >nul 2>&1
if %errorlevel% equ 0 (
    echo 使用TCC编译器...
    tcc fifo.c simple_test.c -o fifo_test.exe
    if %errorlevel% equ 0 (
        echo 编译成功！
        echo 运行测试...
        fifo_test.exe
    ) else (
        echo 编译失败
    )
    goto end
)

echo 错误: 未找到任何C编译器
echo 请安装以下编译器之一:
echo 1. Visual Studio (包含cl.exe)
echo 2. MinGW-w64 (包含gcc.exe)
echo 3. TCC (Tiny C Compiler)

:end
pause
