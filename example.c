/**
 * @file example.c
 * @brief FIFO模块使用示例
 * <AUTHOR> Assistant
 * @date 2025-07-26
 */

#include "fifo.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>

/* ========== 示例数据结构 ========== */

typedef struct {
    int id;
    char name[32];
    double value;
} test_data_t;

/* ========== 错误回调函数示例 ========== */

#if FIFO_ENABLE_ERROR_CALLBACK
void error_callback(fifo_result_t error_code, const char* error_msg, void* user_data) {
    printf("FIFO Error Callback: %s (code: %d)\n", error_msg, error_code);
    if (user_data) {
        printf("User data: %s\n", (char*)user_data);
    }
}
#endif

/* ========== 基本功能测试 ========== */

void test_basic_operations() {
    printf("\n=== 基本操作测试 ===\n");
    
    // 创建FIFO
    fifo_t* fifo = fifo_create(10);
    assert(fifo != NULL);
    printf("✓ FIFO创建成功，容量: %u\n", fifo_capacity(fifo));
    
    // 测试空队列状态
    assert(fifo_is_empty(fifo) == true);
    assert(fifo_is_full(fifo) == false);
    assert(fifo_count(fifo) == 0);
    printf("✓ 空队列状态检查通过\n");
    
    // 入队测试
    int data1 = 100, data2 = 200, data3 = 300;
    assert(fifo_enqueue(fifo, &data1) == FIFO_SUCCESS);
    assert(fifo_enqueue(fifo, &data2) == FIFO_SUCCESS);
    assert(fifo_enqueue(fifo, &data3) == FIFO_SUCCESS);
    printf("✓ 入队操作成功，当前元素数量: %u\n", fifo_count(fifo));
    
    // 查看队头元素
    void* peek_data;
    assert(fifo_peek(fifo, &peek_data) == FIFO_SUCCESS);
    assert(*(int*)peek_data == 100);
    printf("✓ 队头元素查看成功: %d\n", *(int*)peek_data);
    
    // 出队测试
    void* dequeue_data;
    assert(fifo_dequeue(fifo, &dequeue_data) == FIFO_SUCCESS);
    assert(*(int*)dequeue_data == 100);
    printf("✓ 出队操作成功: %d\n", *(int*)dequeue_data);
    
    assert(fifo_dequeue(fifo, &dequeue_data) == FIFO_SUCCESS);
    assert(*(int*)dequeue_data == 200);
    printf("✓ 出队操作成功: %d\n", *(int*)dequeue_data);
    
    // 清空队列
    assert(fifo_clear(fifo) == FIFO_SUCCESS);
    assert(fifo_is_empty(fifo) == true);
    printf("✓ 队列清空成功\n");
    
    // 销毁FIFO
    assert(fifo_destroy(fifo) == FIFO_SUCCESS);
    printf("✓ FIFO销毁成功\n");
}

/* ========== 边界条件测试 ========== */

void test_boundary_conditions() {
    printf("\n=== 边界条件测试 ===\n");
    
    fifo_t* fifo = fifo_create(3); // 小容量测试
    assert(fifo != NULL);
    
    // 填满队列
    int data[5] = {1, 2, 3, 4, 5};
    for (int i = 0; i < 3; i++) {
        assert(fifo_enqueue(fifo, &data[i]) == FIFO_SUCCESS);
    }
    assert(fifo_is_full(fifo) == true);
    printf("✓ 队列已满状态检查通过\n");
    
    // 尝试继续入队（应该失败）
    assert(fifo_enqueue(fifo, &data[3]) == FIFO_ERROR_FULL);
    printf("✓ 满队列入队失败检查通过\n");
    
    // 清空队列
    void* dequeue_data;
    for (int i = 0; i < 3; i++) {
        assert(fifo_dequeue(fifo, &dequeue_data) == FIFO_SUCCESS);
        assert(*(int*)dequeue_data == data[i]);
    }
    assert(fifo_is_empty(fifo) == true);
    printf("✓ 队列清空检查通过\n");
    
    // 尝试从空队列出队（应该失败）
    assert(fifo_dequeue(fifo, &dequeue_data) == FIFO_ERROR_EMPTY);
    printf("✓ 空队列出队失败检查通过\n");
    
    // 尝试查看空队列队头（应该失败）
    assert(fifo_peek(fifo, &dequeue_data) == FIFO_ERROR_EMPTY);
    printf("✓ 空队列查看失败检查通过\n");
    
    fifo_destroy(fifo);
}

/* ========== 结构体数据测试 ========== */

void test_struct_data() {
    printf("\n=== 结构体数据测试 ===\n");
    
    fifo_t* fifo = fifo_create(5);
    assert(fifo != NULL);
    
    // 创建测试数据
    test_data_t test_items[] = {
        {1, "Item1", 1.1},
        {2, "Item2", 2.2},
        {3, "Item3", 3.3}
    };
    
    // 入队结构体数据
    for (int i = 0; i < 3; i++) {
        assert(fifo_enqueue(fifo, &test_items[i]) == FIFO_SUCCESS);
    }
    printf("✓ 结构体数据入队成功\n");
    
    // 出队并验证数据
    void* data;
    for (int i = 0; i < 3; i++) {
        assert(fifo_dequeue(fifo, &data) == FIFO_SUCCESS);
        test_data_t* item = (test_data_t*)data;
        printf("✓ 出队数据: ID=%d, Name=%s, Value=%.1f\n", 
               item->id, item->name, item->value);
        assert(item->id == test_items[i].id);
        assert(strcmp(item->name, test_items[i].name) == 0);
        assert(item->value == test_items[i].value);
    }
    
    fifo_destroy(fifo);
}

/* ========== 批量操作测试 ========== */

#if FIFO_ENABLE_BATCH_OPERATIONS
void test_batch_operations() {
    printf("\n=== 批量操作测试 ===\n");
    
    fifo_t* fifo = fifo_create(10);
    assert(fifo != NULL);
    
    // 准备批量数据
    int batch_data[8] = {10, 20, 30, 40, 50, 60, 70, 80};
    void* data_ptrs[8];
    for (int i = 0; i < 8; i++) {
        data_ptrs[i] = &batch_data[i];
    }
    
    // 批量入队
    uint32_t enqueued;
    assert(fifo_enqueue_batch(fifo, data_ptrs, 5, &enqueued) == FIFO_SUCCESS);
    assert(enqueued == 5);
    printf("✓ 批量入队成功，入队数量: %u\n", enqueued);
    
    // 批量出队
    void* output_ptrs[5];
    uint32_t dequeued;
    assert(fifo_dequeue_batch(fifo, output_ptrs, 3, &dequeued) == FIFO_SUCCESS);
    assert(dequeued == 3);
    printf("✓ 批量出队成功，出队数量: %u\n", dequeued);
    
    // 验证出队数据
    for (uint32_t i = 0; i < dequeued; i++) {
        assert(*(int*)output_ptrs[i] == batch_data[i]);
        printf("  出队数据[%u]: %d\n", i, *(int*)output_ptrs[i]);
    }
    
    fifo_destroy(fifo);
}
#endif

/* ========== 统计信息测试 ========== */

#if FIFO_ENABLE_STATISTICS
void test_statistics() {
    printf("\n=== 统计信息测试 ===\n");
    
    fifo_t* fifo = fifo_create(5);
    assert(fifo != NULL);
    
    int data[10] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    
    // 执行一些操作
    for (int i = 0; i < 5; i++) {
        fifo_enqueue(fifo, &data[i]);
    }
    
    void* dequeue_data;
    for (int i = 0; i < 3; i++) {
        fifo_dequeue(fifo, &dequeue_data);
    }
    
    // 获取统计信息
    fifo_stats_t stats;
    assert(fifo_get_statistics(fifo, &stats) == FIFO_SUCCESS);
    
    printf("✓ 统计信息:\n");
    printf("  入队次数: %llu\n", (unsigned long long)stats.enqueue_count);
    printf("  出队次数: %llu\n", (unsigned long long)stats.dequeue_count);
    printf("  峰值使用量: %u\n", stats.peak_usage);
    printf("  溢出次数: %u\n", stats.overflow_count);
    
    assert(stats.enqueue_count == 5);
    assert(stats.dequeue_count == 3);
    assert(stats.peak_usage == 5);
    
    // 重置统计信息
    assert(fifo_reset_statistics(fifo) == FIFO_SUCCESS);
    assert(fifo_get_statistics(fifo, &stats) == FIFO_SUCCESS);
    assert(stats.enqueue_count == 0);
    printf("✓ 统计信息重置成功\n");
    
    fifo_destroy(fifo);
}
#endif

/* ========== 错误处理测试 ========== */

void test_error_handling() {
    printf("\n=== 错误处理测试 ===\n");
    
    // 测试错误字符串
    printf("✓ 错误字符串测试:\n");
    printf("  FIFO_SUCCESS: %s\n", fifo_get_error_string(FIFO_SUCCESS));
    printf("  FIFO_ERROR_NULL_POINTER: %s\n", fifo_get_error_string(FIFO_ERROR_NULL_POINTER));
    printf("  FIFO_ERROR_FULL: %s\n", fifo_get_error_string(FIFO_ERROR_FULL));
    
#if FIFO_ENABLE_ERROR_CALLBACK
    // 测试错误回调
    fifo_t* fifo = fifo_create(2);
    assert(fifo != NULL);
    
    char user_data[] = "Test callback data";
    assert(fifo_set_error_callback(fifo, error_callback, user_data) == FIFO_SUCCESS);
    printf("✓ 错误回调设置成功\n");
    
    // 触发错误（尝试从空队列出队）
    void* data;
    fifo_dequeue(fifo, &data); // 这会触发错误回调
    
    fifo_destroy(fifo);
#endif
}

/* ========== 主函数 ========== */

int main() {
    printf("FIFO模块测试开始\n");
    printf("配置信息:\n");
    printf("  默认大小: %d\n", FIFO_DEFAULT_SIZE);
    printf("  动态内存: %s\n", FIFO_ENABLE_DYNAMIC_MEMORY ? "启用" : "禁用");
    printf("  线程安全: %s\n", FIFO_ENABLE_THREAD_SAFE ? "启用" : "禁用");
    printf("  统计信息: %s\n", FIFO_ENABLE_STATISTICS ? "启用" : "禁用");
    printf("  批量操作: %s\n", FIFO_ENABLE_BATCH_OPERATIONS ? "启用" : "禁用");
    printf("  错误回调: %s\n", FIFO_ENABLE_ERROR_CALLBACK ? "启用" : "禁用");
    
    test_basic_operations();
    test_boundary_conditions();
    test_struct_data();
    
#if FIFO_ENABLE_BATCH_OPERATIONS
    test_batch_operations();
#endif

#if FIFO_ENABLE_STATISTICS
    test_statistics();
#endif

    test_error_handling();
    
    printf("\n🎉 所有测试通过！FIFO模块工作正常。\n");
    return 0;
}
