#ifndef FIFO_H
#define FIFO_H

/**
 * @file fifo.h
 * @brief FIFO (First In First Out) 队列模块头文件
 * <AUTHOR> Assistant
 * @date 2025-07-26
 */

#include "fifo_config.h"
#include <stddef.h>
#include <stdint.h>
#include <stdbool.h>

#if FIFO_ENABLE_THREAD_SAFE
#if FIFO_THREAD_SAFE_TYPE == 0
#include <pthread.h>
#elif FIFO_THREAD_SAFE_TYPE == 1
#include <windows.h>
#endif
#endif

#ifdef __cplusplus
extern "C" {
#endif

/* ========== 错误码定义 ========== */

/**
 * @brief FIFO操作结果枚举
 */
typedef enum {
    FIFO_SUCCESS = 0,           /**< 操作成功 */
    FIFO_ERROR_NULL_POINTER,    /**< 空指针错误 */
    FIFO_ERROR_INVALID_SIZE,    /**< 无效的大小参数 */
    FIFO_ERROR_MEMORY_ALLOC,    /**< 内存分配失败 */
    FIFO_ERROR_FULL,            /**< FIFO已满 */
    FIFO_ERROR_EMPTY,           /**< FIFO为空 */
    FIFO_ERROR_INVALID_PARAM,   /**< 无效参数 */
    FIFO_ERROR_THREAD_LOCK,     /**< 线程锁操作失败 */
    FIFO_ERROR_NOT_INITIALIZED, /**< 未初始化 */
    FIFO_ERROR_UNKNOWN          /**< 未知错误 */
} fifo_result_t;

/* ========== 数据结构定义 ========== */

#if FIFO_ENABLE_STATISTICS
/**
 * @brief FIFO统计信息结构体
 */
typedef struct {
    uint64_t enqueue_count;     /**< 入队次数 */
    uint64_t dequeue_count;     /**< 出队次数 */
    uint32_t peak_usage;        /**< 峰值使用量 */
    uint32_t overflow_count;    /**< 溢出次数 */
} fifo_stats_t;
#endif

#if FIFO_ENABLE_ERROR_CALLBACK
/**
 * @brief 错误回调函数类型
 * @param error_code 错误码
 * @param error_msg 错误消息
 * @param user_data 用户数据
 */
typedef void (*fifo_error_callback_t)(fifo_result_t error_code, 
                                       const char* error_msg, 
                                       void* user_data);
#endif

/**
 * @brief FIFO结构体
 */
typedef struct fifo {
    void** buffer;              /**< 数据缓冲区 */
    uint32_t size;              /**< 缓冲区大小 */
    uint32_t head;              /**< 队头索引 */
    uint32_t tail;              /**< 队尾索引 */
    uint32_t count;             /**< 当前元素数量 */
    bool is_initialized;        /**< 初始化标志 */
    
#if FIFO_ENABLE_DATA_COPY
    size_t element_size;        /**< 元素大小（用于数据拷贝） */
#endif

#if FIFO_ENABLE_THREAD_SAFE
#if FIFO_THREAD_SAFE_TYPE == 0
    pthread_mutex_t mutex;      /**< pthread互斥锁 */
#elif FIFO_THREAD_SAFE_TYPE == 1
    CRITICAL_SECTION cs;        /**< Windows临界区 */
#else
    volatile int lock;          /**< 自定义锁 */
#endif
#endif

#if FIFO_ENABLE_STATISTICS
    fifo_stats_t stats;         /**< 统计信息 */
#endif

#if FIFO_ENABLE_ERROR_CALLBACK
    fifo_error_callback_t error_callback;  /**< 错误回调函数 */
    void* error_callback_data;             /**< 错误回调用户数据 */
#endif
} fifo_t;

/* ========== 基本操作接口 ========== */

/**
 * @brief 创建FIFO
 * @param size 缓冲区大小
 * @return FIFO指针，失败返回NULL
 */
fifo_t* fifo_create(uint32_t size);

/**
 * @brief 初始化FIFO（用于静态分配的FIFO）
 * @param fifo FIFO指针
 * @param buffer 缓冲区指针
 * @param size 缓冲区大小
 * @return 操作结果
 */
fifo_result_t fifo_init(fifo_t* fifo, void** buffer, uint32_t size);

/**
 * @brief 销毁FIFO
 * @param fifo FIFO指针
 * @return 操作结果
 */
fifo_result_t fifo_destroy(fifo_t* fifo);

/**
 * @brief 入队操作
 * @param fifo FIFO指针
 * @param data 数据指针
 * @return 操作结果
 */
fifo_result_t fifo_enqueue(fifo_t* fifo, void* data);

/**
 * @brief 出队操作
 * @param fifo FIFO指针
 * @param data 输出数据指针
 * @return 操作结果
 */
fifo_result_t fifo_dequeue(fifo_t* fifo, void** data);

/**
 * @brief 查看队头元素（不出队）
 * @param fifo FIFO指针
 * @param data 输出数据指针
 * @return 操作结果
 */
fifo_result_t fifo_peek(fifo_t* fifo, void** data);

/* ========== 查询接口 ========== */

/**
 * @brief 获取FIFO当前元素数量
 * @param fifo FIFO指针
 * @return 元素数量，错误时返回0
 */
uint32_t fifo_count(const fifo_t* fifo);

/**
 * @brief 获取FIFO容量
 * @param fifo FIFO指针
 * @return 容量大小，错误时返回0
 */
uint32_t fifo_capacity(const fifo_t* fifo);

/**
 * @brief 检查FIFO是否为空
 * @param fifo FIFO指针
 * @return true表示为空，false表示非空
 */
bool fifo_is_empty(const fifo_t* fifo);

/**
 * @brief 检查FIFO是否已满
 * @param fifo FIFO指针
 * @return true表示已满，false表示未满
 */
bool fifo_is_full(const fifo_t* fifo);

/**
 * @brief 获取FIFO剩余空间
 * @param fifo FIFO指针
 * @return 剩余空间大小
 */
uint32_t fifo_available_space(const fifo_t* fifo);

/* ========== 控制接口 ========== */

/**
 * @brief 清空FIFO
 * @param fifo FIFO指针
 * @return 操作结果
 */
fifo_result_t fifo_clear(fifo_t* fifo);

/**
 * @brief 重置FIFO（保持容量不变）
 * @param fifo FIFO指针
 * @return 操作结果
 */
fifo_result_t fifo_reset(fifo_t* fifo);

/**
 * @brief 调整FIFO大小
 * @param fifo FIFO指针
 * @param new_size 新的大小
 * @return 操作结果
 */
fifo_result_t fifo_resize(fifo_t* fifo, uint32_t new_size);

/* ========== 批量操作接口 ========== */

#if FIFO_ENABLE_BATCH_OPERATIONS
/**
 * @brief 批量入队
 * @param fifo FIFO指针
 * @param data_array 数据数组
 * @param count 数据数量
 * @param enqueued 实际入队数量（可为NULL）
 * @return 操作结果
 */
fifo_result_t fifo_enqueue_batch(fifo_t* fifo, void** data_array, 
                                  uint32_t count, uint32_t* enqueued);

/**
 * @brief 批量出队
 * @param fifo FIFO指针
 * @param data_array 输出数据数组
 * @param count 请求数量
 * @param dequeued 实际出队数量（可为NULL）
 * @return 操作结果
 */
fifo_result_t fifo_dequeue_batch(fifo_t* fifo, void** data_array, 
                                  uint32_t count, uint32_t* dequeued);
#endif

/* ========== 统计信息接口 ========== */

#if FIFO_ENABLE_STATISTICS
/**
 * @brief 获取统计信息
 * @param fifo FIFO指针
 * @param stats 统计信息输出
 * @return 操作结果
 */
fifo_result_t fifo_get_statistics(const fifo_t* fifo, fifo_stats_t* stats);

/**
 * @brief 重置统计信息
 * @param fifo FIFO指针
 * @return 操作结果
 */
fifo_result_t fifo_reset_statistics(fifo_t* fifo);
#endif

/* ========== 错误处理接口 ========== */

/**
 * @brief 获取错误描述
 * @param error_code 错误码
 * @return 错误描述字符串
 */
const char* fifo_get_error_string(fifo_result_t error_code);

#if FIFO_ENABLE_ERROR_CALLBACK
/**
 * @brief 设置错误回调函数
 * @param fifo FIFO指针
 * @param callback 回调函数
 * @param user_data 用户数据
 * @return 操作结果
 */
fifo_result_t fifo_set_error_callback(fifo_t* fifo, 
                                       fifo_error_callback_t callback, 
                                       void* user_data);
#endif

#ifdef __cplusplus
}
#endif

#endif /* FIFO_H */
