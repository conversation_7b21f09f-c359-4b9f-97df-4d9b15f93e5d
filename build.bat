@echo off
REM FIFO模块 Windows编译脚本
REM Author: AI Assistant
REM Date: 2025-07-26

echo FIFO模块编译脚本
echo.

REM 检查编译器
where gcc >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到gcc编译器
    echo 请安装MinGW-w64或其他GCC发行版
    echo 推荐安装: https://www.mingw-w64.org/
    pause
    exit /b 1
)

REM 创建目录
if not exist obj mkdir obj
if not exist bin mkdir bin

echo 编译FIFO模块...

REM 编译fifo.c
gcc -Wall -Wextra -std=c99 -O2 -g -c fifo.c -o obj/fifo.o
if %errorlevel% neq 0 (
    echo 编译fifo.c失败
    pause
    exit /b 1
)

REM 编译example.c
gcc -Wall -Wextra -std=c99 -O2 -g -c example.c -o obj/example.o
if %errorlevel% neq 0 (
    echo 编译example.c失败
    pause
    exit /b 1
)

REM 链接可执行文件
gcc obj/fifo.o obj/example.o -o bin/fifo_example.exe
if %errorlevel% neq 0 (
    echo 链接失败
    pause
    exit /b 1
)

echo 编译成功！
echo 可执行文件: bin/fifo_example.exe
echo.

REM 询问是否运行
set /p choice="是否运行示例程序? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 运行示例程序...
    echo.
    bin\fifo_example.exe
)

pause
