/**
 * @file simple_test.c
 * @brief FIFO模块简单测试（不依赖复杂功能）
 * <AUTHOR> Assistant
 * @date 2025-07-26
 */

#include "fifo.h"

// 简单的printf实现（如果系统没有stdio.h）
#ifdef _WIN32
#include <windows.h>
void simple_print(const char* str) {
    HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
    DWORD written;
    WriteConsoleA(hConsole, str, strlen(str), &written, NULL);
}
#else
#include <unistd.h>
void simple_print(const char* str) {
    write(STDOUT_FILENO, str, strlen(str));
}
#endif

// 简单的字符串长度计算
int simple_strlen(const char* str) {
    int len = 0;
    while (str[len] != '\0') len++;
    return len;
}

// 简单的整数转字符串
void int_to_str(int num, char* str) {
    int i = 0;
    int is_negative = 0;
    
    if (num < 0) {
        is_negative = 1;
        num = -num;
    }
    
    if (num == 0) {
        str[i++] = '0';
    } else {
        while (num > 0) {
            str[i++] = '0' + (num % 10);
            num /= 10;
        }
    }
    
    if (is_negative) {
        str[i++] = '-';
    }
    
    str[i] = '\0';
    
    // 反转字符串
    int start = 0;
    int end = i - 1;
    while (start < end) {
        char temp = str[start];
        str[start] = str[end];
        str[end] = temp;
        start++;
        end--;
    }
}

int main() {
    simple_print("FIFO模块简单测试开始\n");
    
    // 创建FIFO
    fifo_t* fifo = fifo_create(5);
    if (!fifo) {
        simple_print("错误: FIFO创建失败\n");
        return 1;
    }
    simple_print("✓ FIFO创建成功\n");
    
    // 测试基本状态
    if (fifo_is_empty(fifo)) {
        simple_print("✓ 空队列检查通过\n");
    } else {
        simple_print("✗ 空队列检查失败\n");
        return 1;
    }
    
    // 测试入队
    int data1 = 100;
    int data2 = 200;
    int data3 = 300;
    
    if (fifo_enqueue(fifo, &data1) == FIFO_SUCCESS) {
        simple_print("✓ 第一个数据入队成功\n");
    } else {
        simple_print("✗ 第一个数据入队失败\n");
        return 1;
    }
    
    if (fifo_enqueue(fifo, &data2) == FIFO_SUCCESS) {
        simple_print("✓ 第二个数据入队成功\n");
    } else {
        simple_print("✗ 第二个数据入队失败\n");
        return 1;
    }
    
    if (fifo_enqueue(fifo, &data3) == FIFO_SUCCESS) {
        simple_print("✓ 第三个数据入队成功\n");
    } else {
        simple_print("✗ 第三个数据入队失败\n");
        return 1;
    }
    
    // 检查队列计数
    uint32_t count = fifo_count(fifo);
    char count_str[20];
    int_to_str((int)count, count_str);
    simple_print("当前队列元素数量: ");
    simple_print(count_str);
    simple_print("\n");
    
    if (count == 3) {
        simple_print("✓ 队列计数正确\n");
    } else {
        simple_print("✗ 队列计数错误\n");
        return 1;
    }
    
    // 测试出队
    void* dequeue_data;
    if (fifo_dequeue(fifo, &dequeue_data) == FIFO_SUCCESS) {
        int value = *(int*)dequeue_data;
        char value_str[20];
        int_to_str(value, value_str);
        simple_print("✓ 出队成功，数据: ");
        simple_print(value_str);
        simple_print("\n");
        
        if (value == 100) {
            simple_print("✓ 出队数据正确（FIFO顺序）\n");
        } else {
            simple_print("✗ 出队数据错误\n");
            return 1;
        }
    } else {
        simple_print("✗ 出队失败\n");
        return 1;
    }
    
    // 测试peek
    if (fifo_peek(fifo, &dequeue_data) == FIFO_SUCCESS) {
        int value = *(int*)dequeue_data;
        if (value == 200) {
            simple_print("✓ Peek操作正确\n");
        } else {
            simple_print("✗ Peek操作错误\n");
            return 1;
        }
    } else {
        simple_print("✗ Peek操作失败\n");
        return 1;
    }
    
    // 验证peek不改变队列
    if (fifo_count(fifo) == 2) {
        simple_print("✓ Peek操作不改变队列大小\n");
    } else {
        simple_print("✗ Peek操作改变了队列大小\n");
        return 1;
    }
    
    // 清空队列
    if (fifo_clear(fifo) == FIFO_SUCCESS) {
        simple_print("✓ 队列清空成功\n");
    } else {
        simple_print("✗ 队列清空失败\n");
        return 1;
    }
    
    if (fifo_is_empty(fifo)) {
        simple_print("✓ 清空后队列为空\n");
    } else {
        simple_print("✗ 清空后队列不为空\n");
        return 1;
    }
    
    // 销毁FIFO
    if (fifo_destroy(fifo) == FIFO_SUCCESS) {
        simple_print("✓ FIFO销毁成功\n");
    } else {
        simple_print("✗ FIFO销毁失败\n");
        return 1;
    }
    
    simple_print("\n🎉 所有基本测试通过！FIFO模块工作正常。\n");
    return 0;
}
