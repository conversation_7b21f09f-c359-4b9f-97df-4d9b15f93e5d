# FIFO模块

一个功能完整、高性能的C语言FIFO（先进先出）队列模块，支持多种配置选项和使用场景。

## 特性

- ✅ **多种数据类型支持** - 通过void*指针支持任意数据类型
- ✅ **可配置缓冲区大小** - 支持动态和静态内存分配
- ✅ **线程安全** - 可选的互斥锁保护（pthread/Windows/自定义）
- ✅ **批量操作** - 支持批量入队和出队操作
- ✅ **统计信息** - 记录入队/出队次数、峰值使用量等
- ✅ **错误处理** - 完善的错误码和可选的错误回调机制
- ✅ **内存管理** - 支持动态分配和静态数组两种模式
- ✅ **溢出保护** - 可选的自动覆盖最旧数据功能
- ✅ **调试支持** - 可配置的调试输出和断言

## 文件结构

```
fifo_module/
├── fifo.h          # 主头文件，包含所有接口声明
├── fifo.c          # 核心实现文件
├── fifo_config.h   # 配置文件，定义各种编译选项
├── example.c       # 使用示例和测试代码
├── Makefile        # 编译脚本
└── README.md       # 说明文档
```

## 快速开始

### 1. 编译和运行

```bash
# 编译默认版本
make

# 编译并运行示例
make run

# 编译调试版本
make debug

# 编译线程安全版本
make threadsafe
```

### 2. 基本使用

```c
#include "fifo.h"

int main() {
    // 创建FIFO，容量为100
    fifo_t* fifo = fifo_create(100);
    
    // 入队数据
    int data1 = 42;
    fifo_enqueue(fifo, &data1);
    
    // 出队数据
    void* data;
    if (fifo_dequeue(fifo, &data) == FIFO_SUCCESS) {
        printf("出队数据: %d\n", *(int*)data);
    }
    
    // 销毁FIFO
    fifo_destroy(fifo);
    return 0;
}
```

## 配置选项

在`fifo_config.h`中可以配置以下选项：

### 基本配置
- `FIFO_DEFAULT_SIZE` - 默认缓冲区大小（1024）
- `FIFO_MAX_SIZE` - 最大缓冲区大小（65536）
- `FIFO_MIN_SIZE` - 最小缓冲区大小（16）

### 内存管理
- `FIFO_ENABLE_DYNAMIC_MEMORY` - 启用动态内存分配（1）
- `FIFO_ENABLE_MEMORY_ALIGNMENT` - 启用内存对齐（1）

### 线程安全
- `FIFO_ENABLE_THREAD_SAFE` - 启用线程安全（1）
- `FIFO_THREAD_SAFE_TYPE` - 线程安全实现方式（0=pthread, 1=Windows, 2=自定义）

### 功能特性
- `FIFO_ENABLE_STATISTICS` - 启用统计信息（1）
- `FIFO_ENABLE_DEBUG` - 启用调试模式（0）
- `FIFO_ENABLE_BATCH_OPERATIONS` - 启用批量操作（1）
- `FIFO_ENABLE_ERROR_CALLBACK` - 启用错误回调（1）
- `FIFO_ENABLE_OVERFLOW_PROTECTION` - 启用溢出保护（0）

## API参考

### 基本操作

```c
// 创建和销毁
fifo_t* fifo_create(uint32_t size);
fifo_result_t fifo_destroy(fifo_t* fifo);

// 入队和出队
fifo_result_t fifo_enqueue(fifo_t* fifo, void* data);
fifo_result_t fifo_dequeue(fifo_t* fifo, void** data);
fifo_result_t fifo_peek(fifo_t* fifo, void** data);

// 状态查询
uint32_t fifo_count(const fifo_t* fifo);
uint32_t fifo_capacity(const fifo_t* fifo);
bool fifo_is_empty(const fifo_t* fifo);
bool fifo_is_full(const fifo_t* fifo);
```

### 批量操作

```c
fifo_result_t fifo_enqueue_batch(fifo_t* fifo, void** data_array, 
                                  uint32_t count, uint32_t* enqueued);
fifo_result_t fifo_dequeue_batch(fifo_t* fifo, void** data_array, 
                                  uint32_t count, uint32_t* dequeued);
```

### 统计信息

```c
fifo_result_t fifo_get_statistics(const fifo_t* fifo, fifo_stats_t* stats);
fifo_result_t fifo_reset_statistics(fifo_t* fifo);
```

### 错误处理

```c
const char* fifo_get_error_string(fifo_result_t error_code);
fifo_result_t fifo_set_error_callback(fifo_t* fifo, 
                                       fifo_error_callback_t callback, 
                                       void* user_data);
```

## 错误码

- `FIFO_SUCCESS` - 操作成功
- `FIFO_ERROR_NULL_POINTER` - 空指针错误
- `FIFO_ERROR_INVALID_SIZE` - 无效的大小参数
- `FIFO_ERROR_MEMORY_ALLOC` - 内存分配失败
- `FIFO_ERROR_FULL` - FIFO已满
- `FIFO_ERROR_EMPTY` - FIFO为空
- `FIFO_ERROR_INVALID_PARAM` - 无效参数
- `FIFO_ERROR_THREAD_LOCK` - 线程锁操作失败
- `FIFO_ERROR_NOT_INITIALIZED` - 未初始化

## 编译选项

```bash
# 基本编译
make

# 不同配置版本
make debug          # 调试版本
make threadsafe     # 线程安全版本
make static         # 静态内存版本
make performance    # 性能优化版本

# 代码质量检查
make memcheck       # 内存检查（需要valgrind）
make coverage       # 代码覆盖率（需要gcov）
make analyze        # 静态分析（需要cppcheck）

# 库文件
make lib           # 创建静态库
make shared        # 创建共享库

# 其他
make clean         # 清理编译文件
make help          # 显示帮助信息
```

## 使用场景

1. **生产者-消费者模式** - 多线程环境下的数据传递
2. **缓冲区管理** - 网络数据包、音视频帧等的缓存
3. **任务队列** - 异步任务的排队处理
4. **数据流处理** - 实时数据的流式处理
5. **事件系统** - 事件的排队和分发

## 性能特点

- **时间复杂度**: 入队和出队操作均为O(1)
- **空间复杂度**: O(n)，其中n为队列容量
- **线程安全**: 可选的互斥锁保护，支持多线程并发访问
- **内存效率**: 支持预分配和内存对齐优化

## 注意事项

1. **数据生命周期**: 模块只存储指针，不负责数据的内存管理
2. **线程安全**: 需要根据使用场景选择合适的线程安全配置
3. **错误处理**: 建议检查所有API的返回值
4. **配置选择**: 根据实际需求调整配置参数以获得最佳性能

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个模块。
