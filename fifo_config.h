#ifndef FIFO_CONFIG_H
#define FIFO_CONFIG_H

/**
 * @file fifo_config.h
 * @brief FIFO模块配置文件
 * <AUTHOR> Assistant
 * @date 2025-07-26
 */

/* ========== 基本配置 ========== */

/**
 * @brief 默认FIFO缓冲区大小
 */
#ifndef FIFO_DEFAULT_SIZE
#define FIFO_DEFAULT_SIZE 1024
#endif

/**
 * @brief 最大FIFO缓冲区大小
 */
#ifndef FIFO_MAX_SIZE
#define FIFO_MAX_SIZE 65536
#endif

/**
 * @brief 最小FIFO缓冲区大小
 */
#ifndef FIFO_MIN_SIZE
#define FIFO_MIN_SIZE 16
#endif

/* ========== 内存管理配置 ========== */

/**
 * @brief 启用动态内存分配
 * 1: 使用malloc/free动态分配内存
 * 0: 使用静态数组（需要在编译时确定大小）
 */
#ifndef FIFO_ENABLE_DYNAMIC_MEMORY
#define FIFO_ENABLE_DYNAMIC_MEMORY 1
#endif

/**
 * @brief 启用内存对齐
 * 1: 启用内存对齐优化
 * 0: 不进行内存对齐
 */
#ifndef FIFO_ENABLE_MEMORY_ALIGNMENT
#define FIFO_ENABLE_MEMORY_ALIGNMENT 1
#endif

/**
 * @brief 内存对齐字节数
 */
#ifndef FIFO_MEMORY_ALIGNMENT
#define FIFO_MEMORY_ALIGNMENT 8
#endif

/* ========== 线程安全配置 ========== */

/**
 * @brief 启用线程安全
 * 1: 启用互斥锁保护
 * 0: 不使用线程安全机制（单线程环境）
 */
#ifndef FIFO_ENABLE_THREAD_SAFE
#define FIFO_ENABLE_THREAD_SAFE 1
#endif

/**
 * @brief 线程安全实现方式
 * 0: 使用标准库pthread
 * 1: 使用Windows API
 * 2: 使用自定义锁实现
 */
#ifndef FIFO_THREAD_SAFE_TYPE
#ifdef _WIN32
#define FIFO_THREAD_SAFE_TYPE 1
#else
#define FIFO_THREAD_SAFE_TYPE 0
#endif
#endif

/* ========== 功能特性配置 ========== */

/**
 * @brief 启用统计信息
 * 1: 记录入队/出队次数、峰值使用量等统计信息
 * 0: 不记录统计信息
 */
#ifndef FIFO_ENABLE_STATISTICS
#define FIFO_ENABLE_STATISTICS 1
#endif

/**
 * @brief 启用调试模式
 * 1: 启用调试输出和额外的错误检查
 * 0: 关闭调试功能
 */
#ifndef FIFO_ENABLE_DEBUG
#define FIFO_ENABLE_DEBUG 0
#endif

/**
 * @brief 启用数据拷贝模式
 * 1: 入队时拷贝数据，出队时返回拷贝
 * 0: 只存储指针，不拷贝数据
 */
#ifndef FIFO_ENABLE_DATA_COPY
#define FIFO_ENABLE_DATA_COPY 0
#endif

/**
 * @brief 启用溢出保护
 * 1: 当FIFO满时，自动覆盖最旧的数据
 * 0: 当FIFO满时，拒绝新数据
 */
#ifndef FIFO_ENABLE_OVERFLOW_PROTECTION
#define FIFO_ENABLE_OVERFLOW_PROTECTION 0
#endif

/* ========== 错误处理配置 ========== */

/**
 * @brief 启用详细错误信息
 * 1: 提供详细的错误描述
 * 0: 只返回错误码
 */
#ifndef FIFO_ENABLE_ERROR_DETAILS
#define FIFO_ENABLE_ERROR_DETAILS 1
#endif

/**
 * @brief 错误回调函数
 * 1: 支持注册错误回调函数
 * 0: 不支持错误回调
 */
#ifndef FIFO_ENABLE_ERROR_CALLBACK
#define FIFO_ENABLE_ERROR_CALLBACK 1
#endif

/* ========== 性能优化配置 ========== */

/**
 * @brief 启用批量操作
 * 1: 支持批量入队/出队操作
 * 0: 只支持单个元素操作
 */
#ifndef FIFO_ENABLE_BATCH_OPERATIONS
#define FIFO_ENABLE_BATCH_OPERATIONS 1
#endif

/**
 * @brief 启用预分配
 * 1: 预分配内存以减少动态分配次数
 * 0: 按需分配内存
 */
#ifndef FIFO_ENABLE_PREALLOCATION
#define FIFO_ENABLE_PREALLOCATION 1
#endif

/* ========== 调试配置 ========== */

/**
 * @brief 调试输出宏
 */
#if FIFO_ENABLE_DEBUG
#include <stdio.h>
#define FIFO_DEBUG_PRINT(fmt, ...) \
    printf("[FIFO DEBUG] " fmt "\n", ##__VA_ARGS__)
#else
#define FIFO_DEBUG_PRINT(fmt, ...)
#endif

/**
 * @brief 断言宏
 */
#if FIFO_ENABLE_DEBUG
#include <assert.h>
#define FIFO_ASSERT(condition) assert(condition)
#else
#define FIFO_ASSERT(condition)
#endif

#endif /* FIFO_CONFIG_H */
